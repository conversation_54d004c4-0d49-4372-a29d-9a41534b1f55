* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #0a0f1c 0%, #1a2a3a 50%, #0a0f1c 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
}

/* 标题栏样式 */
.header {
    height: 80px;
    background: linear-gradient(90deg, #1e4a72 0%, #2a6bb8 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    box-shadow: 0 4px 20px rgba(0, 180, 255, 0.4);
    position: relative;
    z-index: 1000;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 180, 255, 0.15) 50%, transparent 100%);
    animation: headerGlow 3s ease-in-out infinite alternate;
}

@keyframes headerGlow {
    0% { opacity: 0.4; }
    100% { opacity: 0.9; }
}

.header h1 {
    font-size: 32px;
    font-weight: bold;
    color: #00e4ff;
    text-shadow: 0 0 25px rgba(0, 228, 255, 0.6);
    position: relative;
    z-index: 1;
}

.header-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.datetime {
    font-size: 18px;
    color: #a0d8ff;
}

.control-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.control-group label {
    color: #a0d8ff;
    font-size: 14px;
}

.control-group select {
    background: rgba(0, 150, 255, 0.1);
    border: 1px solid #0096ff;
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
}

.control-group select:focus {
    outline: none;
    box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
}

/* 主要内容区域 */
.main-content {
    height: calc(100vh - 80px);
    display: grid;
    grid-template-rows: 1fr 350px;
    gap: 20px;
    padding: 20px;
}

/* 上半部分：地图和指标 */
.top-section {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 20px;
    position: relative;
}

/* 地图容器 */
.map-container {
    position: relative;
    background: rgba(0, 20, 40, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(0, 150, 255, 0.3);
    overflow: hidden;
}

.map-chart {
    width: 100%;
    height: 100%;
}

/* 指标卡片容器 */
.metrics-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    padding: 20px;
    background: rgba(0, 20, 40, 0.6);
    border-radius: 10px;
    border: 1px solid rgba(0, 150, 255, 0.3);
    backdrop-filter: blur(10px);
}

.metric-card {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 100, 200, 0.05) 100%);
    border: 1px solid rgba(0, 150, 255, 0.2);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 150, 255, 0.1), transparent);
    transition: left 0.5s;
}

.metric-card:hover::before {
    left: 100%;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 150, 255, 0.3);
}

.metric-label {
    font-size: 16px;
    color: #b0e8ff;
    margin-bottom: 8px;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(176, 232, 255, 0.4);
}

.metric-value {
    font-size: 22px;
    font-weight: bold;
    color: #00e4ff;
    margin-bottom: 5px;
    text-shadow: 0 0 15px rgba(0, 228, 255, 0.5);
}

.metric-change {
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.metric-change.positive {
    color: #00ff88;
}

.metric-change.negative {
    color: #ff4757;
}

.change-arrow {
    font-size: 10px;
}

/* 排行榜容器 */
.rankings-container {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 340px;
    height: 540px;
    background: rgba(0, 20, 40, 0.9);
    border-radius: 10px;
    border: 1px solid rgba(0, 150, 255, 0.3);
    padding: 20px;
    backdrop-filter: blur(10px);
    z-index: 100;
}

/* 移动端排行榜样式 */
.rankings-container.mobile-rankings {
    position: static;
    width: 100%;
    height: auto;
    margin-top: 15px;
    padding: 15px;
}

.ranking-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(0, 150, 255, 0.2);
}

.ranking-tab {
    flex: 1;
    padding: 8px 4px;
    text-align: center;
    font-size: 12px;
    color: #a0d8ff;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.ranking-tab.active {
    color: #00d4ff;
    border-bottom-color: #00d4ff;
}

.ranking-list {
    height: 460px;
    overflow-y: auto;
    padding-right: 5px;
}

.ranking-item {
    display: flex;
    align-items: center;
    padding: 15px 8px;
    border-bottom: 1px solid rgba(0, 180, 255, 0.15);
    height: 70px;
    margin-bottom: 6px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.ranking-item:hover {
    background: rgba(0, 150, 255, 0.08);
    transform: translateX(3px);
}

.ranking-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
    font-size: 14px;
}

.ranking-number.first {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #000;
}

.ranking-number.second {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    color: #000;
}

.ranking-number.third {
    background: linear-gradient(135deg, #cd7f32, #daa520);
    color: #fff;
}

.ranking-number.other {
    background: rgba(0, 150, 255, 0.2);
    color: #00d4ff;
}

.ranking-info {
    flex: 1;
}

.ranking-name {
    font-size: 13px;
    color: #ffffff;
    margin-bottom: 6px;
    line-height: 1.3;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.ranking-progress {
    width: 100%;
    height: 10px;
    background: rgba(0, 180, 255, 0.15);
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 4px;
}

.ranking-progress-bar {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
    position: relative;
    overflow: hidden;
}

.ranking-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.ranking-progress-bar.rank-1 {
    background: linear-gradient(90deg, #ff6b35, #f7931e);
}

.ranking-progress-bar.rank-2 {
    background: linear-gradient(90deg, #00e4ff, #0099cc);
}

.ranking-progress-bar.rank-3 {
    background: linear-gradient(90deg, #7b68ee, #9370db);
}

.ranking-progress-bar.rank-4 {
    background: linear-gradient(90deg, #32cd32, #228b22);
}

.ranking-progress-bar.rank-5 {
    background: linear-gradient(90deg, #ffd700, #ffb347);
}

.ranking-progress-bar.rank-6 {
    background: linear-gradient(90deg, #ff69b4, #ff1493);
}

.ranking-progress-bar.rank-7 {
    background: linear-gradient(90deg, #40e0d0, #00ced1);
}

.ranking-value {
    font-size: 13px;
    color: #a0d8ff;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(160, 216, 255, 0.3);
}

/* 下半部分：趋势图表 */
.bottom-section {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.chart-container {
    background: rgba(0, 30, 60, 0.7);
    border-radius: 12px;
    border: 1px solid rgba(0, 180, 255, 0.4);
    padding: 15px;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 180, 255, 0.05) 0%, rgba(0, 100, 200, 0.02) 100%);
    pointer-events: none;
}

.chart-title {
    font-size: 18px;
    color: #00e4ff;
    margin-bottom: 10px;
    text-align: center;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 228, 255, 0.4);
    position: relative;
    z-index: 1;
}

.chart {
    width: 100%;
    height: calc(100% - 50px);
    min-height: 280px;
    position: relative;
    z-index: 1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 150, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 150, 255, 0.5);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 150, 255, 0.7);
}

/* Desktop Responsive Design */
/* Large Desktop (1920x1080) */
@media screen and (min-width: 1920px) {
    .header {
        height: 90px;
        padding: 0 60px;
    }

    .header h1 {
        font-size: 36px;
    }

    .main-content {
        height: calc(100vh - 90px);
        padding: 30px;
        gap: 30px;
        grid-template-rows: 1fr 380px;
    }

    .top-section {
        grid-template-columns: 1fr 400px;
        gap: 30px;
    }

    .metrics-container {
        padding: 30px;
        gap: 20px;
    }

    .metric-card {
        padding: 20px;
    }

    .metric-value {
        font-size: 26px;
    }

    .chart-title {
        font-size: 20px;
    }
}

/* Standard Desktop (1440x900, 1600x900) */
@media screen and (min-width: 1440px) and (max-width: 1919px) {
    .header {
        height: 85px;
        padding: 0 50px;
    }

    .header h1 {
        font-size: 34px;
    }

    .main-content {
        height: calc(100vh - 85px);
        padding: 25px;
        gap: 25px;
        grid-template-rows: 1fr 360px;
    }

    .top-section {
        grid-template-columns: 1fr 380px;
        gap: 25px;
    }

    .metrics-container {
        padding: 25px;
        gap: 18px;
    }

    .metric-card {
        padding: 18px;
    }

    .metric-value {
        font-size: 24px;
    }

    .chart-title {
        font-size: 19px;
    }
}

/* Laptop (1366x768) */
@media screen and (min-width: 1366px) and (max-width: 1439px) {
    .header {
        height: 75px;
        padding: 0 30px;
    }

    .header h1 {
        font-size: 28px;
    }

    .header-controls {
        gap: 15px;
    }

    .datetime {
        font-size: 16px;
    }

    .main-content {
        height: calc(100vh - 75px);
        padding: 15px;
        gap: 15px;
        grid-template-rows: 1fr 280px;
    }

    .top-section {
        grid-template-columns: 1fr 320px;
        gap: 15px;
    }

    .metrics-container {
        padding: 15px;
        gap: 12px;
    }

    .metric-card {
        padding: 12px;
    }

    .metric-label {
        font-size: 14px;
    }

    .metric-value {
        font-size: 20px;
    }

    .metric-change {
        font-size: 10px;
    }

    .chart-title {
        font-size: 16px;
        margin-bottom: 8px;
    }

    .chart {
        min-height: 200px;
    }

    .rankings-container {
        width: 300px;
        height: 480px;
        padding: 15px;
    }

    .ranking-list {
        height: 400px;
    }
}

/* Mobile Responsive Design */
/* Tablet Portrait (768px - 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
    body {
        overflow-y: auto;
        height: auto;
        min-height: 100vh;
    }

    .header {
        height: auto;
        padding: 10px 15px;
        flex-direction: column;
        gap: 8px;
    }

    .header h1 {
        font-size: 20px;
        margin-bottom: 5px;
        line-height: 1.2;
    }

    .header-controls {
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .datetime {
        font-size: 12px;
        order: -1;
        width: 100%;
        text-align: center;
        padding: 3px 0;
    }

    .main-content {
        height: auto;
        min-height: calc(100vh - 80px);
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .top-section {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    /* Hide map on tablet and mobile */
    .map-container {
        display: none;
    }

    .metrics-container {
        grid-template-columns: 1fr 1fr;
        padding: 20px;
        gap: 15px;
    }

    .bottom-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .chart-container {
        min-height: 300px;
    }

    .rankings-container.mobile-rankings .ranking-list {
        height: 300px;
    }
}

/* Mobile Landscape (480px - 767px) */
@media screen and (min-width: 480px) and (max-width: 767px) {
    body {
        overflow-y: auto;
        height: auto;
        min-height: 100vh;
    }

    .header {
        height: auto;
        padding: 8px 12px;
        flex-direction: column;
        gap: 8px;
    }

    .header h1 {
        font-size: 18px;
        text-align: center;
        line-height: 1.2;
        margin-bottom: 3px;
    }

    .header-controls {
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .control-group {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .control-group select {
        padding: 10px 15px;
        font-size: 16px;
        min-height: 44px; /* Touch-friendly */
    }

    .datetime {
        font-size: 12px;
        text-align: center;
        width: 100%;
        padding: 2px 0;
    }

    .main-content {
        height: auto;
        min-height: calc(100vh - 90px);
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 12px;
    }

    .top-section {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    /* Hide map on mobile */
    .map-container {
        display: none;
    }

    .metrics-container {
        grid-template-columns: 1fr 1fr;
        padding: 15px;
        gap: 10px;
    }

    .metric-card {
        padding: 15px 10px;
        min-height: 80px;
    }

    .metric-label {
        font-size: 13px;
    }

    .metric-value {
        font-size: 18px;
    }

    .bottom-section {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .chart-container {
        min-height: 280px;
        padding: 15px;
    }

    .chart-title {
        font-size: 16px;
    }

    .rankings-container.mobile-rankings .ranking-list {
        height: 250px;
    }
}

/* Mobile Portrait (320px - 479px) */
@media screen and (max-width: 479px) {
    body {
        overflow-y: auto;
        height: auto;
        min-height: 100vh;
    }

    .header {
        height: auto;
        padding: 8px;
        flex-direction: column;
        gap: 6px;
    }

    .header h1 {
        font-size: 16px;
        text-align: center;
        line-height: 1.1;
        margin-bottom: 2px;
    }

    .header-controls {
        gap: 6px;
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .control-group label {
        font-size: 12px;
    }

    .control-group select {
        padding: 12px 15px;
        font-size: 16px;
        min-height: 48px; /* Touch-friendly */
        width: 100%;
    }

    .datetime {
        font-size: 11px;
        text-align: center;
        padding: 2px 0;
    }

    .main-content {
        height: auto;
        min-height: calc(100vh - 100px);
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 8px;
    }

    .top-section {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    /* Hide map completely on mobile */
    .map-container {
        display: none !important;
    }

    .metrics-container {
        grid-template-columns: 1fr;
        padding: 10px;
        gap: 8px;
    }

    .metric-card {
        padding: 12px;
        min-height: 70px;
        text-align: center;
    }

    .metric-label {
        font-size: 12px;
        margin-bottom: 5px;
    }

    .metric-value {
        font-size: 16px;
        margin-bottom: 3px;
    }

    .metric-change {
        font-size: 9px;
    }

    .bottom-section {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .chart-container {
        min-height: 250px;
        padding: 10px;
    }

    .chart-title {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .chart {
        min-height: 200px;
    }

    .rankings-container.mobile-rankings {
        margin-top: 8px;
        padding: 8px;
    }

    .rankings-container.mobile-rankings .ranking-tabs {
        margin-bottom: 8px;
    }

    .rankings-container.mobile-rankings .ranking-tab {
        padding: 6px 2px;
        font-size: 10px;
    }

    .rankings-container.mobile-rankings .ranking-list {
        height: 200px;
    }

    .ranking-item {
        padding: 8px;
        margin-bottom: 5px;
    }

    .ranking-number {
        font-size: 12px;
        width: 20px;
        height: 20px;
        line-height: 20px;
    }

    .ranking-name {
        font-size: 11px;
    }

    .ranking-value {
        font-size: 10px;
    }
}

/* Utility classes for mobile */
@media screen and (max-width: 1023px) {
    .desktop-only {
        display: none !important;
    }

    .mobile-only {
        display: block !important;
    }

    /* Touch-friendly buttons and inputs */
    button, select, input {
        min-height: 44px;
        touch-action: manipulation;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
        font-size: 16px;
    }
}