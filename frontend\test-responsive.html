<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式设计测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .screen-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .test-item {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .responsive-indicator {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        /* Desktop indicators */
        .desktop-large { background: #4caf50; color: white; display: none; }
        .desktop-standard { background: #2196f3; color: white; display: none; }
        .laptop { background: #ff9800; color: white; display: none; }
        .tablet { background: #9c27b0; color: white; display: none; }
        .mobile-landscape { background: #f44336; color: white; display: none; }
        .mobile-portrait { background: #e91e63; color: white; display: none; }
        
        /* Media queries matching our CSS */
        @media screen and (min-width: 1920px) {
            .desktop-large { display: block; }
        }
        
        @media screen and (min-width: 1440px) and (max-width: 1919px) {
            .desktop-standard { display: block; }
        }
        
        @media screen and (min-width: 1366px) and (max-width: 1439px) {
            .laptop { display: block; }
        }
        
        @media screen and (min-width: 768px) and (max-width: 1023px) {
            .tablet { display: block; }
        }
        
        @media screen and (min-width: 480px) and (max-width: 767px) {
            .mobile-landscape { display: block; }
        }
        
        @media screen and (max-width: 479px) {
            .mobile-portrait { display: block; }
        }
        
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .test-button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>方松院数据看板 - 响应式设计测试</h1>
        
        <div class="screen-info">
            <h3>当前屏幕信息</h3>
            <p>窗口宽度: <span id="window-width"></span>px</p>
            <p>窗口高度: <span id="window-height"></span>px</p>
            <p>设备像素比: <span id="device-ratio"></span></p>
        </div>
        
        <div class="responsive-indicator desktop-large">
            🖥️ 大型桌面 (≥1920px) - 适用于1920x1080显示器
        </div>
        <div class="responsive-indicator desktop-standard">
            🖥️ 标准桌面 (1440px-1919px) - 适用于1440x900, 1600x900显示器
        </div>
        <div class="responsive-indicator laptop">
            💻 笔记本 (1366px-1439px) - 适用于1366x768显示器
        </div>
        <div class="responsive-indicator tablet">
            📱 平板 (768px-1023px) - 地图已隐藏
        </div>
        <div class="responsive-indicator mobile-landscape">
            📱 手机横屏 (480px-767px) - 地图已隐藏，触摸优化
        </div>
        <div class="responsive-indicator mobile-portrait">
            📱 手机竖屏 (≤479px) - 地图已隐藏，单列布局
        </div>
        
        <div class="test-buttons">
            <button class="test-button" onclick="resizeWindow(1920, 1080)">测试 1920x1080</button>
            <button class="test-button" onclick="resizeWindow(1600, 900)">测试 1600x900</button>
            <button class="test-button" onclick="resizeWindow(1440, 900)">测试 1440x900</button>
            <button class="test-button" onclick="resizeWindow(1366, 768)">测试 1366x768</button>
            <button class="test-button" onclick="resizeWindow(1024, 768)">测试 平板</button>
            <button class="test-button" onclick="resizeWindow(768, 1024)">测试 平板竖屏</button>
            <button class="test-button" onclick="resizeWindow(667, 375)">测试 iPhone</button>
            <button class="test-button" onclick="resizeWindow(360, 640)">测试 Android</button>
        </div>
        
        <div class="test-grid">
            <div class="test-item">
                <h4>桌面特性 ✅</h4>
                <ul style="text-align: left;">
                    <li>✅ 显示地图组件</li>
                    <li>✅ 趋势图表完整显示</li>
                    <li>✅ 排行榜绝对定位</li>
                    <li>✅ 无滚动条</li>
                </ul>
            </div>
            <div class="test-item">
                <h4>移动特性 ✅</h4>
                <ul style="text-align: left;">
                    <li>✅ 隐藏地图组件</li>
                    <li>✅ 优化的紧凑头部</li>
                    <li>✅ 排行榜独立显示</li>
                    <li>✅ 触摸友好界面</li>
                </ul>
            </div>
            <div class="test-item">
                <h4>修复内容 🔧</h4>
                <ul style="text-align: left;">
                    <li>🔧 桌面趋势图高度增加</li>
                    <li>🔧 移动头部高度优化</li>
                    <li>🔧 排行榜移动端可见</li>
                    <li>🔧 响应式布局改进</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>应用预览</h2>
        <p>在下方iframe中查看实际应用的响应式效果：</p>
        <div class="iframe-container">
            <iframe src="http://localhost:3001/" title="方松院数据看板"></iframe>
        </div>
    </div>

    <script>
        function updateScreenInfo() {
            document.getElementById('window-width').textContent = window.innerWidth;
            document.getElementById('window-height').textContent = window.innerHeight;
            document.getElementById('device-ratio').textContent = window.devicePixelRatio;
        }
        
        function resizeWindow(width, height) {
            // Note: This won't actually resize the window in most browsers due to security restrictions
            // But it shows the intended test dimensions
            alert(`测试尺寸: ${width}x${height}\n\n请手动调整浏览器窗口大小到此尺寸，或使用开发者工具的设备模拟器。`);
        }
        
        // Update screen info on load and resize
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
        
        // Auto-refresh iframe every 5 seconds to show changes
        setInterval(() => {
            const iframe = document.querySelector('iframe');
            if (iframe) {
                iframe.src = iframe.src;
            }
        }, 5000);
    </script>
</body>
</html>
