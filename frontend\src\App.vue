<template>
  <div id="app">
    <!-- 标题栏 -->
    <div class="header">
      <h1>方松院连锁门店数据看板</h1>
      <div class="header-controls">
        <div class="datetime">{{ currentDateTime }}</div>
        <div class="control-group">
          <label>门店:</label>
          <select v-model="selectedStore" @change="onStoreChange">
            <option v-for="store in stores" :key="store.id" :value="store.id">
              {{ store.name }}
            </option>
          </select>
        </div>
        <div class="control-group">
          <label>时间:</label>
          <select v-model="selectedTimeRange" @change="onTimeRangeChange">
            <option value="today">今日</option>
            <option value="yesterday">昨日</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 上半部分：地图和指标 -->
      <div class="top-section">
        <!-- 地图容器 - 仅在桌面端显示 -->
        <div v-if="!isMobile" class="map-container">
          <div ref="mapChart" class="map-chart"></div>

          <!-- 排行榜容器 - 桌面端绝对定位在地图上方 -->
          <div class="rankings-container">
            <div class="ranking-tabs">
              <div
                v-for="tab in rankingTabs"
                :key="tab.type"
                :class="['ranking-tab', { active: currentRankingType === tab.type }]"
                @click="switchRankingTab(tab.type)"
              >
                {{ tab.label }}
              </div>
            </div>
            <div class="ranking-list">
              <div
                v-for="item in currentRankings"
                :key="item.rank"
                class="ranking-item"
              >
                <div :class="['ranking-number', getRankClass(item.rank)]">
                  {{ item.rank }}
                </div>
                <div class="ranking-info">
                  <div class="ranking-name">{{ item.name }}</div>
                  <div class="ranking-progress">
                    <div
                      :class="['ranking-progress-bar', `rank-${item.rank}`]"
                      :style="{ width: item.percentage + '%' }"
                    ></div>
                  </div>
                  <div class="ranking-value">{{ formatRankingValue(item.value, currentRankingType) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 指标卡片容器 -->
        <div class="metrics-container">
          <div
            v-for="metric in metrics"
            :key="metric.key"
            class="metric-card"
          >
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-value">{{ metric.formattedValue }}</div>
            <div :class="['metric-change', metric.change > 0 ? 'positive' : 'negative']">
              <span class="change-arrow">{{ metric.change > 0 ? '↗' : '↘' }}</span>
              <span>{{ metric.change > 0 ? '+' : '' }}{{ metric.change }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 移动端排行榜容器 - 仅在移动端显示 -->
      <div v-if="isMobile" class="rankings-container mobile-rankings">
        <div class="ranking-tabs">
          <div
            v-for="tab in rankingTabs"
            :key="tab.type"
            :class="['ranking-tab', { active: currentRankingType === tab.type }]"
            @click="switchRankingTab(tab.type)"
          >
            {{ tab.label }}
          </div>
        </div>
        <div class="ranking-list">
          <div
            v-for="item in currentRankings"
            :key="item.rank"
            class="ranking-item"
          >
            <div :class="['ranking-number', getRankClass(item.rank)]">
              {{ item.rank }}
            </div>
            <div class="ranking-info">
              <div class="ranking-name">{{ item.name }}</div>
              <div class="ranking-progress">
                <div
                  :class="['ranking-progress-bar', `rank-${item.rank}`]"
                  :style="{ width: item.percentage + '%' }"
                ></div>
              </div>
              <div class="ranking-value">{{ formatRankingValue(item.value, currentRankingType) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 下半部分：趋势图表 -->
      <div class="bottom-section">
        <div class="chart-container">
          <div class="chart-title">总营业额趋势</div>
          <div ref="revenueChart" class="chart"></div>
        </div>
        <div class="chart-container">
          <div class="chart-title">总客流趋势</div>
          <div ref="trafficChart" class="chart"></div>
        </div>
        <div class="chart-container">
          <div class="chart-title">总充值金额趋势</div>
          <div ref="rechargeChart" class="chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { apiService } from './services/api.js'
import { mockData } from './services/mockData.js'
import changshaGeoJson from './assets/changsha.json'
import wuhanGeoJson from './assets/wuhan.json'

export default {
  name: 'App',
  setup() {
    // 响应式数据
    const currentDateTime = ref('')
    const selectedStore = ref('all')
    const selectedTimeRange = ref('today')
    const currentRankingType = ref('revenue')
    const windowWidth = ref(window.innerWidth)
    
    // 图表实例
    const mapChart = ref(null)
    const revenueChart = ref(null)
    const trafficChart = ref(null)
    const rechargeChart = ref(null)
    
    // 图表对象
    let mapChartInstance = null
    let revenueChartInstance = null
    let trafficChartInstance = null
    let rechargeChartInstance = null
    
    // 定时器
    let dateTimeTimer = null
    let autoSwitchTimer = null
    
    // 数据
    const stores = ref([
      { id: 'all', name: '全部门店' },
      { id: 'store1', name: '长沙岳麓店' },
      { id: 'store2', name: '长沙芙蓉店' },
      { id: 'store3', name: '长沙天心店' },
      { id: 'store4', name: '长沙开福店' },
      { id: 'store5', name: '长沙雨花店' },
      { id: 'store6', name: '武汉江汉店' },
      { id: 'store7', name: '武汉武昌店' }
    ])
    
    const rankingTabs = [
      { type: 'revenue', label: '营业额' },
      { type: 'traffic', label: '客流' },
      { type: 'staff', label: '人数' },
      { type: 'commission', label: '提成' }
    ]
    
    const metrics = ref([])
    const currentRankings = ref([])

    // 计算属性
    const isMobile = computed(() => {
      return windowWidth.value <= 1023
    })
    
    // 工具函数
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toLocaleString()
    }
    
    const formatCurrency = (num) => {
      if (num >= 10000) {
        return '¥' + (num / 10000).toFixed(1) + '万'
      }
      return '¥' + num.toLocaleString()
    }
    
    const formatRankingValue = (value, type) => {
      if (type === 'revenue' || type === 'commission') {
        return formatCurrency(value)
      } else if (type === 'traffic' || type === 'staff') {
        return formatNumber(value) + '人'
      }
      return value
    }
    
    const getRankClass = (rank) => {
      if (rank === 1) return 'first'
      if (rank === 2) return 'second'
      if (rank === 3) return 'third'
      return 'other'
    }
    
    // 更新日期时间
    const updateDateTime = () => {
      const now = new Date()
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        weekday: 'long',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }
      currentDateTime.value = now.toLocaleDateString('zh-CN', options)
    }
    
    // 更新指标数据
    const updateMetrics = async () => {
      try {
        const storeId = selectedStore.value === 'all' ? null : selectedStore.value
        const data = await apiService.fetchAnalysisSummary(storeId, selectedTimeRange.value)
        
        const metricsData = data.metrics
        metrics.value = [
          {
            key: 'totalRevenue',
            label: '总营业额',
            value: metricsData.totalRevenue?.value || 0,
            change: metricsData.totalRevenue?.change || 0,
            formattedValue: formatCurrency(metricsData.totalRevenue?.value || 0)
          },
          {
            key: 'totalCashFlow',
            label: '总现金流',
            value: metricsData.totalCashFlow?.value || 0,
            change: metricsData.totalCashFlow?.change || 0,
            formattedValue: formatCurrency(metricsData.totalCashFlow?.value || 0)
          },
          {
            key: 'totalTraffic',
            label: '总客流',
            value: metricsData.totalTraffic?.value || 0,
            change: metricsData.totalTraffic?.change || 0,
            formattedValue: formatNumber(metricsData.totalTraffic?.value || 0) + '人'
          },
          {
            key: 'totalCardConsumption',
            label: '总耗卡金额',
            value: metricsData.totalCardConsumption?.value || 0,
            change: metricsData.totalCardConsumption?.change || 0,
            formattedValue: formatCurrency(metricsData.totalCardConsumption?.value || 0)
          },
          {
            key: 'totalMeituan',
            label: '总美团金额',
            value: metricsData.totalMeituan?.value || 0,
            change: metricsData.totalMeituan?.change || 0,
            formattedValue: formatCurrency(metricsData.totalMeituan?.value || 0)
          },
          {
            key: 'totalDouyin',
            label: '总抖音金额',
            value: metricsData.totalDouyin?.value || 0,
            change: metricsData.totalDouyin?.change || 0,
            formattedValue: formatCurrency(metricsData.totalDouyin?.value || 0)
          },
          {
            key: 'totalOnline',
            label: '总线上消费金额',
            value: metricsData.totalOnline?.value || 0,
            change: metricsData.totalOnline?.change || 0,
            formattedValue: formatCurrency(metricsData.totalOnline?.value || 0)
          },
          {
            key: 'totalOffline',
            label: '总线下消费金额',
            value: metricsData.totalOffline?.value || 0,
            change: metricsData.totalOffline?.change || 0,
            formattedValue: formatCurrency(metricsData.totalOffline?.value || 0)
          },
          {
            key: 'totalCard',
            label: '总卡金额',
            value: metricsData.totalCard?.value || 0,
            change: metricsData.totalCard?.change || 0,
            formattedValue: formatCurrency(metricsData.totalCard?.value || 0)
          },
          {
            key: 'totalReviews',
            label: '好评总量',
            value: metricsData.totalReviews?.value || 0,
            change: metricsData.totalReviews?.change || 0,
            formattedValue: formatNumber(metricsData.totalReviews?.value || 0) + '条'
          }
        ]
      } catch (error) {
        console.error('更新指标数据失败:', error)
        // 使用模拟数据
        const mockMetrics = mockData.getAnalysisSummary().metrics
        metrics.value = [
          {
            key: 'totalRevenue',
            label: '总营业额',
            value: mockMetrics.totalRevenue.value,
            change: mockMetrics.totalRevenue.change,
            formattedValue: formatCurrency(mockMetrics.totalRevenue.value)
          }
          // ... 其他指标
        ]
      }
    }
    
    // 更新排行榜
    const updateRankings = async (type = 'revenue') => {
      try {
        const data = await apiService.fetchRankings(type, selectedTimeRange.value)
        let rankings = data.rankings
        
        if (!rankings || rankings.length === 0) {
          rankings = mockData.getRankings(type).rankings || []
        }
        
        // 计算百分比
        const maxValue = Math.max(...rankings.map(r => r.value))
        currentRankings.value = rankings.map(item => ({
          ...item,
          percentage: (item.value / maxValue) * 100
        }))
        
        // 更新地图数据
        updateMapData(type)
      } catch (error) {
        console.error('更新排行榜失败:', error)
        const rankings = mockData.getRankings(type).rankings || []
        const maxValue = Math.max(...rankings.map(r => r.value))
        currentRankings.value = rankings.map(item => ({
          ...item,
          percentage: (item.value / maxValue) * 100
        }))
      }
    }
    
    // 更新地图数据
    const updateMapData = (type = 'revenue') => {
      if (!mapChartInstance) return
      
      // 使用模拟数据更新地图
      const mapData = mockData.getMapData()
      const changshaStores = mapData.stores.filter(store => store.name.includes('长沙'))
      const wuhanStores = mapData.stores.filter(store => store.name.includes('武汉'))
      
      const option = mapChartInstance.getOption()
      
      // 更新数据
      const getStoreValue = (store, type) => {
        switch(type) {
          case 'revenue': return store.value
          case 'traffic': return store.traffic
          case 'reviews': return store.reviews
          default: return store.value
        }
      }
      
      option.series[0].data = changshaStores.map(store => ({
        name: store.name,
        value: store.coord.concat(getStoreValue(store, type))
      }))
      
      option.series[1].data = wuhanStores.map(store => ({
        name: store.name,
        value: store.coord.concat(getStoreValue(store, type))
      }))
      
      mapChartInstance.setOption(option, true)
    }
    
    // 初始化地图
    const initMapChart = async () => {
      if (!mapChart.value) return
      
      mapChartInstance = echarts.init(mapChart.value)
      
      try {
        // 注册地图数据
        echarts.registerMap('changsha', changshaGeoJson)
        echarts.registerMap('wuhan', wuhanGeoJson)
        
        // 使用完整地图配置
        initMapWithGeo()
      } catch (error) {
        console.error('地图数据加载失败:', error)
        // 使用简化地图配置
        initMapWithoutGeo()
      }
    }
    
    // 带地理数据的地图初始化
    const initMapWithGeo = () => {
      const option = {
        backgroundColor: 'transparent',
        geo: [
          {
            map: 'changsha',
            roam: true,
            zoom: 1.2,
            center: [113.0823, 28.2070],
            left: '2%',
            right: '53%',
            top: '8%',
            bottom: '8%',
            itemStyle: {
              areaColor: 'rgba(0, 60, 120, 0.4)',
              borderColor: 'rgba(0, 180, 255, 0.6)',
              borderWidth: 1.5
            },
            emphasis: {
              itemStyle: {
                areaColor: 'rgba(0, 180, 255, 0.3)'
              }
            },
            label: {
              show: true,
              color: '#a0d8ff',
              fontSize: 11
            }
          },
          {
            map: 'wuhan',
            roam: true,
            zoom: 1.2,
            center: [114.3054, 30.5931],
            left: '53%',
            right: '2%',
            top: '8%',
            bottom: '8%',
            itemStyle: {
              areaColor: 'rgba(0, 60, 120, 0.4)',
              borderColor: 'rgba(0, 180, 255, 0.6)',
              borderWidth: 1.5
            },
            emphasis: {
              itemStyle: {
                areaColor: 'rgba(0, 180, 255, 0.3)'
              }
            },
            label: {
              show: true,
              color: '#a0d8ff',
              fontSize: 11
            }
          }
        ],
        series: [
          // 长沙热力图
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            data: mockData.getMapData().stores.filter(store => store.name.includes('长沙')).map(store => ({
              name: store.name,
              value: store.coord.concat(store.revenue)
            })),
            symbolSize: function(val) {
              return Math.max(Math.sqrt(val[2] / 10000) + 15, 20)
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'fill',
              scale: 2,
              period: 4
            },
            itemStyle: {
              color: 'rgba(0, 228, 255, 0.6)',
              shadowBlur: 20,
              shadowColor: 'rgba(0, 228, 255, 0.8)'
            }
          },
          // 武汉热力图
          {
            type: 'effectScatter',
            coordinateSystem: 'geo',
            geoIndex: 1,
            data: mockData.getMapData().stores.filter(store => store.name.includes('武汉')).map(store => ({
              name: store.name,
              value: store.coord.concat(store.revenue)
            })),
            symbolSize: function(val) {
              return Math.max(Math.sqrt(val[2] / 10000) + 15, 20)
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'fill',
              scale: 2,
              period: 4
            },
            itemStyle: {
              color: 'rgba(255, 107, 53, 0.6)',
              shadowBlur: 20,
              shadowColor: 'rgba(255, 107, 53, 0.8)'
            }
          }
        ],
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 30, 60, 0.95)',
          borderColor: 'rgba(0, 180, 255, 0.6)',
          borderWidth: 2,
          textStyle: {
            color: '#ffffff',
            fontSize: 14
          },
          formatter: function(params) {
            return `<div style="padding: 8px;">
              <div style="font-size: 16px; font-weight: bold; color: #00e4ff; margin-bottom: 5px;">${params.name}</div>
              <div style="color: #b0e8ff;">营业额: ${formatCurrency(params.value[2])}</div>
            </div>`
          }
        }
      }
      
      mapChartInstance.setOption(option)
    }
    
    // 不带地理数据的地图初始化
    const initMapWithoutGeo = () => {
      const option = {
        backgroundColor: 'transparent',
        grid: {
          left: '10%',
          right: '10%',
          top: '10%',
          bottom: '10%'
        },
        xAxis: {
          type: 'value',
          min: 112.5,
          max: 115,
          show: false
        },
        yAxis: {
          type: 'value',
          min: 27.5,
          max: 31,
          show: false
        },
        series: [{
          type: 'scatter',
          data: mockData.getMapData().stores.map(store => ({
            name: store.name,
            value: [store.coord[0], store.coord[1], store.revenue]
          })),
          symbolSize: function(val) {
            return Math.sqrt(val[2] / 10000) + 15
          },
          itemStyle: {
            color: {
              type: 'radial',
              x: 0.5, y: 0.5, r: 0.5,
              colorStops: [
                { offset: 0, color: '#00e4ff' },
                { offset: 0.7, color: '#0099cc' },
                { offset: 1, color: 'rgba(0, 153, 204, 0.3)' }
              ]
            },
            shadowBlur: 15,
            shadowColor: 'rgba(0, 228, 255, 0.8)'
          },
          label: {
            show: true,
            position: 'top',
            color: '#ffffff',
            fontSize: 13,
            fontWeight: 'bold',
            formatter: '{b}'
          }
        }],
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 30, 60, 0.95)',
          borderColor: 'rgba(0, 180, 255, 0.6)',
          borderWidth: 2,
          textStyle: {
            color: '#ffffff',
            fontSize: 14
          },
          formatter: function(params) {
            return `<div style="padding: 8px;">
              <div style="font-size: 16px; font-weight: bold; color: #00e4ff; margin-bottom: 5px;">${params.name}</div>
              <div style="color: #b0e8ff;">营业额: ${formatCurrency(params.value[2])}</div>
            </div>`
          }
        }
      }
      
      mapChartInstance.setOption(option)
    }
    
    // 初始化趋势图表
    const initTrendCharts = async () => {
      const shopId = selectedStore.value === 'all' ? null : selectedStore.value
      
      // 获取趋势数据
      let revenueData, trafficData, rechargeData
      try {
        [revenueData, trafficData, rechargeData] = await Promise.all([
          apiService.fetchTrendsData('revenue', shopId, 7),
          apiService.fetchTrendsData('traffic', shopId, 7),
          apiService.fetchTrendsData('recharge', shopId, 7)
        ])
      } catch (error) {
        console.error('获取趋势数据失败:', error)
        // 使用模拟数据
        revenueData = mockData.getTrends('revenue').values
          trafficData = mockData.getTrends('traffic').values
          rechargeData = mockData.getTrends('revenue').values
      }
      
      // 营业额趋势图
      if (revenueChart.value) {
        revenueChartInstance = echarts.init(revenueChart.value)
        const revenueOption = {
          backgroundColor: 'transparent',
          grid: {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '15%'
          },
          xAxis: {
            type: 'category',
            data: revenueData.dates,
            axisLine: {
              lineStyle: { color: 'rgba(0, 180, 255, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#b0e8ff',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: { color: 'rgba(0, 180, 255, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#b0e8ff',
              fontSize: 11,
              fontWeight: 'bold',
              formatter: formatCurrency
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 180, 255, 0.15)',
                type: 'dashed'
              }
            }
          },
          series: [{
            type: 'line',
            data: revenueData.values,
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(0, 228, 255, 0.9)' },
                  { offset: 0.5, color: 'rgba(0, 228, 255, 0.5)' },
                  { offset: 1, color: 'rgba(0, 228, 255, 0.05)' }
                ]
              }
            },
            lineStyle: { width: 0, opacity: 0 },
            itemStyle: { opacity: 0 },
            symbol: 'none'
          }],
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 30, 60, 0.95)',
            borderColor: 'rgba(0, 180, 255, 0.6)',
            borderWidth: 2,
            textStyle: { color: '#ffffff', fontSize: 14 },
            formatter: function(params) {
              return `<div style="padding: 8px;">
                <div style="color: #00e4ff; font-size: 16px; margin-bottom: 5px;">${params[0].name}</div>
                <div style="color: #b0e8ff;">营业额: ${formatCurrency(params[0].value)}</div>
              </div>`
            }
          }
        }
        revenueChartInstance.setOption(revenueOption)
      }
      
      // 客流趋势图
      if (trafficChart.value) {
        trafficChartInstance = echarts.init(trafficChart.value)
        const trafficOption = {
          backgroundColor: 'transparent',
          grid: {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '15%'
          },
          xAxis: {
            type: 'category',
            data: trafficData.dates,
            axisLine: {
              lineStyle: { color: 'rgba(255, 107, 53, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#ffb399',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: { color: 'rgba(255, 107, 53, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#ffb399',
              fontSize: 11,
              fontWeight: 'bold',
              formatter: (value) => formatNumber(value) + '人'
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255, 107, 53, 0.15)',
                type: 'dashed'
              }
            }
          },
          series: [{
            type: 'line',
            data: trafficData.values,
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(255, 107, 53, 0.9)' },
                  { offset: 0.5, color: 'rgba(255, 107, 53, 0.5)' },
                  { offset: 1, color: 'rgba(255, 107, 53, 0.05)' }
                ]
              }
            },
            lineStyle: { width: 0, opacity: 0 },
            itemStyle: { opacity: 0 },
            symbol: 'none'
          }],
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 30, 60, 0.95)',
            borderColor: 'rgba(255, 107, 53, 0.6)',
            borderWidth: 2,
            textStyle: { color: '#ffffff', fontSize: 14 },
            formatter: function(params) {
              return `<div style="padding: 8px;">
                <div style="color: #ff6b35; font-size: 16px; margin-bottom: 5px;">${params[0].name}</div>
                <div style="color: #ffb399;">客流量: ${formatNumber(params[0].value)}人</div>
              </div>`
            }
          }
        }
        trafficChartInstance.setOption(trafficOption)
      }
      
      // 充值金额趋势图
      if (rechargeChart.value) {
        rechargeChartInstance = echarts.init(rechargeChart.value)
        const rechargeOption = {
          backgroundColor: 'transparent',
          grid: {
            left: '8%',
            right: '5%',
            top: '15%',
            bottom: '15%'
          },
          xAxis: {
            type: 'category',
            data: rechargeData.dates,
            axisLine: {
              lineStyle: { color: 'rgba(102, 255, 102, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#b3ffb3',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: { color: 'rgba(102, 255, 102, 0.6)', width: 2 }
            },
            axisLabel: {
              color: '#b3ffb3',
              fontSize: 11,
              fontWeight: 'bold',
              formatter: formatCurrency
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(102, 255, 102, 0.15)',
                type: 'dashed'
              }
            }
          },
          series: [{
            type: 'line',
            data: rechargeData.values,
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(102, 255, 102, 0.9)' },
                  { offset: 0.5, color: 'rgba(102, 255, 102, 0.5)' },
                  { offset: 1, color: 'rgba(102, 255, 102, 0.05)' }
                ]
              }
            },
            lineStyle: { width: 0, opacity: 0 },
            itemStyle: { opacity: 0 },
            symbol: 'none'
          }],
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 30, 60, 0.95)',
            borderColor: 'rgba(102, 255, 102, 0.6)',
            borderWidth: 2,
            textStyle: { color: '#ffffff', fontSize: 14 },
            formatter: function(params) {
              return `<div style="padding: 8px;">
                <div style="color: #66ff66; font-size: 16px; margin-bottom: 5px;">${params[0].name}</div>
                <div style="color: #b3ffb3;">充值金额: ${formatCurrency(params[0].value)}</div>
              </div>`
            }
          }
        }
        rechargeChartInstance.setOption(rechargeOption)
      }
    }
    
    // 自动切换排行榜
    const startAutoSwitch = () => {
      const rankingTypes = ['revenue', 'traffic', 'staff', 'commission']
      let currentIndex = 0
      
      autoSwitchTimer = setInterval(() => {
        currentIndex = (currentIndex + 1) % rankingTypes.length
        currentRankingType.value = rankingTypes[currentIndex]
        updateRankings(currentRankingType.value)
      }, 5000)
    }
    
    const stopAutoSwitch = () => {
      if (autoSwitchTimer) {
        clearInterval(autoSwitchTimer)
        autoSwitchTimer = null
      }
    }
    
    // 事件处理
    const onStoreChange = () => {
      updateMetrics()
      nextTick(() => {
        initTrendCharts()
      })
    }
    
    const onTimeRangeChange = () => {
      updateMetrics()
      updateRankings(currentRankingType.value)
    }
    
    const switchRankingTab = (type) => {
      stopAutoSwitch()
      currentRankingType.value = type
      updateRankings(type)
      
      setTimeout(() => {
        startAutoSwitch()
      }, 10000)
    }
    
    // 窗口大小变化处理
    const handleResize = () => {
      windowWidth.value = window.innerWidth
      if (mapChartInstance && !isMobile.value) mapChartInstance.resize()
      if (revenueChartInstance) revenueChartInstance.resize()
      if (trafficChartInstance) trafficChartInstance.resize()
      if (rechargeChartInstance) rechargeChartInstance.resize()
    }
    
    // 生命周期
    onMounted(async () => {
      // 更新日期时间
      updateDateTime()
      dateTimeTimer = setInterval(updateDateTime, 1000)
      
      // 等待DOM渲染完成
      await nextTick()
      
      // 初始化图表
      if (!isMobile.value) {
        await initMapChart()
      }
      await initTrendCharts()
      
      // 初始化数据
      updateMetrics()
      updateRankings()
      
      // 启动自动切换
      startAutoSwitch()
      
      // 监听窗口大小变化
      window.addEventListener('resize', handleResize)
    })
    
    onUnmounted(() => {
      if (dateTimeTimer) clearInterval(dateTimeTimer)
      if (autoSwitchTimer) clearInterval(autoSwitchTimer)
      window.removeEventListener('resize', handleResize)
      
      // 销毁图表实例
      if (mapChartInstance) mapChartInstance.dispose()
      if (revenueChartInstance) revenueChartInstance.dispose()
      if (trafficChartInstance) trafficChartInstance.dispose()
      if (rechargeChartInstance) rechargeChartInstance.dispose()
    })
    
    return {
      // 响应式数据
      currentDateTime,
      selectedStore,
      selectedTimeRange,
      currentRankingType,
      stores,
      rankingTabs,
      metrics,
      currentRankings,

      // 计算属性
      isMobile,

      // 模板引用
      mapChart,
      revenueChart,
      trafficChart,
      rechargeChart,

      // 方法
      formatRankingValue,
      getRankClass,
      onStoreChange,
      onTimeRangeChange,
      switchRankingTab
    }
  }
}
</script>